{"endOfLine": "lf", "semi": false, "singleQuote": false, "tabWidth": 2, "trailingComma": "es5", "importOrder": ["^(react/(.*)$)|^(react$)", "^(next/(.*)$)|^(next$)", "<THIRD_PARTY_MODULES>", "", "^types$", "^@/types/(.*)$", "^@/config/(.*)$", "^@/lib/(.*)$", "^@/hooks/(.*)$", "^@/components/ui/(.*)$", "^@/components/(.*)$", "^@/styles/(.*)$", "^@/app/(.*)$", "^@/(.*)$", "", "^@cvx/(.*)$", "", "^[./]"], "importOrderParserPlugins": ["typescript", "jsx", "decorators-legacy"], "importOrderTypeScriptVersion": "5.0.0", "tailwindFunctions": ["cn"], "plugins": ["@ianvs/prettier-plugin-sort-imports", "prettier-plugin-tailwindcss"]}