{"name": "mys", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prettier": "npx prettier . --write"}, "dependencies": {"@auth/core": "^0.37.4", "@convex-dev/auth": "^0.0.80", "@hookform/resolvers": "^5.0.1", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^15.3.2", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.6", "@react-aria/button": "^3.13.0", "@react-aria/i18n": "^3.12.8", "@react-aria/numberfield": "^3.11.13", "@react-pdf/renderer": "^4.3.0", "@react-stately/numberfield": "^3.9.11", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "convex": "^1.24.1", "convex-helpers": "^0.1.85", "dayjs": "^1.11.13", "dayjs-plugin-lunar": "^1.4.1", "file-saver": "^2.0.5", "jiti": "^1.21.7", "lucide-react": "^0.483.0", "next": "^15.3.2", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "nuqs": "^2.4.3", "opencc-js": "^1.0.5", "react": "^19.1.0", "react-day-picker": "^9.6.7", "react-dom": "^19.1.0", "react-hook-form": "^7.56.2", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "usehooks-ts": "^3.1.1", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@ianvs/prettier-plugin-sort-imports": "^4.4.1", "@tailwindcss/postcss": "^4.1.5", "@tailwindcss/typography": "^0.5.16", "@types/file-saver": "^2.0.7", "@types/mdx": "^2.0.13", "@types/node": "^20.17.43", "@types/opencc-js": "^1.0.3", "@types/react": "^19.1.3", "@types/react-dom": "^19.1.3", "eslint": "^9.26.0", "eslint-config-next": "^15.3.2", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.1.1", "tw-animate-css": "^1.2.9", "typescript": "^5.8.3"}}